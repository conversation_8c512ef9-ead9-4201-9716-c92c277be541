/**
 * Enhanced fullscreen utilities with mobile browser compatibility
 */

// Type definitions for browser-specific fullscreen APIs
interface DocumentWithFullscreen extends Document {
  webkitFullscreenElement?: Element;
  mozFullScreenElement?: Element;
  msFullscreenElement?: Element;
  webkitExitFullscreen?: () => Promise<void>;
  mozCancelFullScreen?: () => Promise<void>;
  msExitFullscreen?: () => Promise<void>;
}

interface ElementWithFullscreen extends Element {
  webkitRequestFullscreen?: () => Promise<void>;
  mozRequestFullScreen?: () => Promise<void>;
  msRequestFullscreen?: () => Promise<void>;
}

/**
 * Check if fullscreen is currently active across different browsers
 */
export function isFullscreenActive(): boolean {
  const doc = document as DocumentWithFullscreen;
  return !!(
    doc.fullscreenElement ||
    doc.webkitFullscreenElement ||
    doc.mozFullScreenElement ||
    doc.msFullscreenElement
  );
}

/**
 * Check if fullscreen API is supported
 */
export function isFullscreenSupported(): boolean {
  const docElement = document.documentElement as ElementWithFullscreen;
  return !!(
    docElement.requestFullscreen ||
    docElement.webkitRequestFullscreen ||
    docElement.mozRequestFullScreen ||
    docElement.msRequestFullscreen
  );
}

/**
 * Request fullscreen with cross-browser compatibility
 */
export async function requestFullscreen(): Promise<boolean> {
  try {
    const docElement = document.documentElement as ElementWithFullscreen;

    console.log('Attempting to request fullscreen...', getFullscreenDebugInfo());

    if (docElement.requestFullscreen) {
      console.log('Using standard requestFullscreen API');
      await docElement.requestFullscreen();
    } else if (docElement.webkitRequestFullscreen) {
      console.log('Using webkit requestFullscreen API');
      await docElement.webkitRequestFullscreen();
    } else if (docElement.mozRequestFullScreen) {
      console.log('Using moz requestFullscreen API');
      await docElement.mozRequestFullScreen();
    } else if (docElement.msRequestFullscreen) {
      console.log('Using ms requestFullscreen API');
      await docElement.msRequestFullscreen();
    } else {
      console.warn('Fullscreen API not supported on this device');
      return false;
    }
    console.log('Fullscreen request successful');
    return true;
  } catch (err) {
    console.error('Failed to request fullscreen:', err);
    return false;
  }
}

/**
 * Exit fullscreen with cross-browser compatibility
 */
export async function exitFullscreen(): Promise<boolean> {
  try {
    const doc = document as DocumentWithFullscreen;
    
    if (doc.exitFullscreen) {
      await doc.exitFullscreen();
    } else if (doc.webkitExitFullscreen) {
      await doc.webkitExitFullscreen();
    } else if (doc.mozCancelFullScreen) {
      await doc.mozCancelFullScreen();
    } else if (doc.msExitFullscreen) {
      await doc.msExitFullscreen();
    } else {
      console.warn('Exit fullscreen not supported');
      return false;
    }
    return true;
  } catch (err) {
    console.error('Failed to exit fullscreen:', err);
    return false;
  }
}

/**
 * Toggle fullscreen state
 */
export async function toggleFullscreen(): Promise<boolean> {
  if (isFullscreenActive()) {
    return await exitFullscreen();
  } else {
    return await requestFullscreen();
  }
}

/**
 * Add fullscreen change event listeners with cross-browser compatibility
 */
export function addFullscreenChangeListener(callback: () => void): () => void {
  const events = [
    'fullscreenchange',
    'webkitfullscreenchange',
    'mozfullscreenchange',
    'MSFullscreenChange'
  ];

  events.forEach(event => {
    document.addEventListener(event, callback);
  });

  // Return cleanup function
  return () => {
    events.forEach(event => {
      document.removeEventListener(event, callback);
    });
  };
}

/**
 * Get device and browser information for debugging
 */
export function getFullscreenDebugInfo() {
  const userAgent = navigator.userAgent;
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isAndroid = /Android/.test(userAgent);
  const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
  
  return {
    userAgent,
    isMobile,
    isIOS,
    isAndroid,
    isSafari,
    isFullscreenSupported: isFullscreenSupported(),
    isFullscreenActive: isFullscreenActive(),
    availableAPIs: {
      standard: !!document.documentElement.requestFullscreen,
      webkit: !!(document.documentElement as any).webkitRequestFullscreen,
      moz: !!(document.documentElement as any).mozRequestFullScreen,
      ms: !!(document.documentElement as any).msRequestFullscreen,
    }
  };
}

/**
 * Show user-friendly error message for unsupported devices
 */
export function showFullscreenUnsupportedMessage(): void {
  const debugInfo = getFullscreenDebugInfo();

  let message = 'Fullscreen mode is not supported on this device.';

  if (debugInfo.isIOS) {
    message = 'Fullscreen mode is not supported on iOS Safari. You can manually enter fullscreen by tapping the fullscreen icon in the browser controls.';
  } else if (debugInfo.isMobile && debugInfo.isSafari) {
    message = 'Fullscreen mode has limited support on mobile Safari. Try using Chrome or Firefox for better fullscreen experience.';
  }

  console.warn(message, debugInfo);

  // Show alert for debugging on mobile
  if (debugInfo.isMobile) {
    alert(`Fullscreen Debug Info:\n${message}\n\nDevice: ${debugInfo.isMobile ? 'Mobile' : 'Desktop'}\nBrowser: ${debugInfo.isSafari ? 'Safari' : 'Other'}\nSupported: ${debugInfo.isFullscreenSupported}`);
  }
}
