"use client";

import { useState, useEffect } from "react";
import { 
  isFullscreenActive, 
  isFullscreenSupported, 
  toggleFullscreen, 
  getFullscreenDebugInfo,
  addFullscreenChangeListener 
} from "@/lib/fullscreen";

export function FullscreenDebug() {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  useEffect(() => {
    // Update debug info
    setDebugInfo(getFullscreenDebugInfo());
    
    // Listen for fullscreen changes
    const cleanup = addFullscreenChangeListener(() => {
      setIsFullScreen(isFullscreenActive());
      setDebugInfo(getFullscreenDebugInfo());
    });

    return cleanup;
  }, []);

  const handleToggleFullscreen = async () => {
    console.log('Debug: Toggle fullscreen clicked');
    const success = await toggleFullscreen();
    console.log('Debug: Toggle result:', success);
    
    if (!success) {
      alert('Fullscreen failed! Check console for details.');
    }
  };

  if (!debugInfo) {
    return <div>Loading debug info...</div>;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-xs z-50">
      <h3 className="font-bold mb-2">Fullscreen Debug</h3>
      
      <div className="space-y-1 mb-3">
        <div>Status: {isFullScreen ? '✅ Active' : '❌ Inactive'}</div>
        <div>Supported: {debugInfo.isFullscreenSupported ? '✅ Yes' : '❌ No'}</div>
        <div>Mobile: {debugInfo.isMobile ? '📱 Yes' : '🖥️ No'}</div>
        <div>iOS: {debugInfo.isIOS ? '🍎 Yes' : '❌ No'}</div>
        <div>Android: {debugInfo.isAndroid ? '🤖 Yes' : '❌ No'}</div>
        <div>Safari: {debugInfo.isSafari ? '🧭 Yes' : '❌ No'}</div>
      </div>

      <div className="space-y-1 mb-3 text-xs">
        <div className="font-semibold">Available APIs:</div>
        <div>Standard: {debugInfo.availableAPIs.standard ? '✅' : '❌'}</div>
        <div>Webkit: {debugInfo.availableAPIs.webkit ? '✅' : '❌'}</div>
        <div>Moz: {debugInfo.availableAPIs.moz ? '✅' : '❌'}</div>
        <div>MS: {debugInfo.availableAPIs.ms ? '✅' : '❌'}</div>
      </div>

      <button
        onClick={handleToggleFullscreen}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm"
      >
        {isFullScreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
      </button>
      
      <div className="mt-2 text-xs opacity-75">
        UA: {debugInfo.userAgent.substring(0, 50)}...
      </div>
    </div>
  );
}
