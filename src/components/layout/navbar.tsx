"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Clock, AlarmClock, Timer, TimerOff, Maximize, Minimize } from "lucide-react";
import { cn } from "@/lib/utils";
import { isFullscreenActive, toggleFullscreen, addFullscreenChangeListener } from "@/lib/fullscreen";

export function Navbar() {
  const pathname = usePathname();
  const [isFullScreen, setIsFullScreen] = useState(false);

  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(isFullscreenActive());
    };

    // Use the utility function to add cross-browser event listeners
    const cleanup = addFullscreenChangeListener(handleFullScreenChange);

    return cleanup;
  }, []);

  const handleToggleFullScreen = async () => {
    await toggleFullscreen();
  };

  const navItems = [
    { href: "/", label: "Time", icon: <Clock className="h-5 w-5" /> },
    { href: "/alarm", label: "Alarm", icon: <AlarmClock className="h-5 w-5" /> },
    { href: "/timer", label: "Timer", icon: <Timer className="h-5 w-5" /> },
    { href: "/stopwatch", label: "Stopwatch", icon: <TimerOff className="h-5 w-5" /> },
  ];

  return (
    <nav className={cn(
      "fixed top-0 left-0 right-0 z-40 transition-all duration-300",
      isFullScreen && "opacity-0 hover:opacity-100"
    )}>
      {/* Desktop Navigation */}
      <div className="hidden md:block">
        <div className="mx-6 mt-6">
          <div className="bg-white/40 dark:bg-gray-900/40 backdrop-blur-xl border border-white/20 dark:border-gray-700/50 shadow-2xl rounded-3xl">
            <div className="px-8 py-4">
              <div className="flex items-center justify-between">
                {/* Logo */}
                <div className="flex items-center">
                  <Link href="/" className="group flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#ffffff"><g fill="none" stroke="#ffffff" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"><circle cx="12" cy="13" r="8"/><path d="M5 3L2 6m20 0l-3-3M6.38 18.7L4 21m13.64-2.33L20 21M9 13l2 2l4-4"/></g></svg>                    
                    </div>
                    <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      BestOnlineClock
                    </span>
                  </Link>
                </div>

                {/* Navigation Items */}
                <div className="flex items-center space-x-2">
                  {navItems.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        "flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-200",
                        pathname === item.href
                          ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg"
                          : "text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/50 hover:shadow-md"
                      )}
                    >
                      {item.icon}
                      <span>{item.label}</span>
                    </Link>
                  ))}
                </div>

                {/* Fullscreen Button */}
                <div className="flex items-center">
                  <button
                    onClick={handleToggleFullScreen}
                    className="group relative w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 hover:from-blue-500 hover:to-purple-600 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center"
                    aria-label={isFullScreen ? "Exit full screen" : "Enter full screen"}
                  >
                    {isFullScreen ? (
                      <Minimize className="h-5 w-5 text-gray-600 dark:text-gray-300 group-hover:text-white transition-colors duration-300" />
                    ) : (
                      <Maximize className="h-5 w-5 text-gray-600 dark:text-gray-300 group-hover:text-white transition-colors duration-300" />
                    )}
                    
                    {/* Tooltip */}
                    <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      {isFullScreen ? "Exit fullscreen" : "Enter fullscreen"}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        <div className="mx-4 mt-4">
          <div className="bg-white/40 dark:bg-gray-900/40 backdrop-blur-xl border border-white/20 dark:border-gray-700/50 shadow-2xl rounded-2xl">
            {/* Mobile Header */}
            <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200/50 dark:border-gray-700/50">
              <Link href="/" className="group flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#ffffff"><g fill="none" stroke="#ffffff" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"><circle cx="12" cy="13" r="8"/><path d="M5 3L2 6m20 0l-3-3M6.38 18.7L4 21m13.64-2.33L20 21M9 13l2 2l4-4"/></g></svg> 
                </div>
                <span className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  BestOnlineClock
                </span>
              </Link>
              
              <button
                onClick={handleToggleFullScreen}
                className="group relative w-9 h-9 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 hover:from-blue-500 hover:to-purple-600 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center"
                aria-label={isFullScreen ? "Exit full screen" : "Enter full screen"}
              >
                {isFullScreen ? (
                  <Minimize className="h-4 w-4 text-gray-600 dark:text-gray-300 group-hover:text-white transition-colors duration-300" />
                ) : (
                  <Maximize className="h-4 w-4 text-gray-600 dark:text-gray-300 group-hover:text-white transition-colors duration-300" />
                )}
                
                {/* Tooltip for mobile */}
                <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  {isFullScreen ? "Exit" : "Fullscreen"}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
                </div>
              </button>
            </div>

            {/* Mobile Navigation Items */}
            <div className="grid grid-cols-4 gap-1 p-2">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex flex-col items-center justify-center py-3 px-2 rounded-xl text-xs font-medium transition-all duration-200",
                    pathname === item.href
                      ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg"
                      : "text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/50"
                  )}
                >
                  <div className={cn(
                    "mb-1 transition-transform duration-200",
                    pathname === item.href ? "scale-110" : "group-hover:scale-105"
                  )}>
                    {item.icon}
                  </div>
                  <span>{item.label}</span>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
