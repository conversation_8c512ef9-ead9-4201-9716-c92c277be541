"use client";

import { useState, useEffect } from "react";
import { Navbar } from "./navbar";
import { SimulatedFullscreenExit } from "@/components/ui/simulated-fullscreen-exit";
import { isFullscreenActive, isSimulatedFullscreenActive, addFullscreenChangeListener } from "@/lib/fullscreen";

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isSimulatedFullScreen, setIsSimulatedFullScreen] = useState(false);

  useEffect(() => {
    const handleFullScreenChange = () => {
      const isFullScreenNow = isFullscreenActive();
      const isSimulatedNow = isSimulatedFullscreenActive();

      setIsFullScreen(isFullScreenNow);
      setIsSimulatedFullScreen(isSimulatedNow);
    };

    // Use the utility function to add cross-browser event listeners
    const cleanup = addFullscreenChangeListener(handleFullScreenChange);

    return cleanup;
  }, []);

  return (
    <div className="min-h-screen text-foreground">
      {/* Show navbar only when not in fullscreen mode */}
      {!isFullScreen && <Navbar />}

      {/* Show simulated fullscreen exit button when in simulated fullscreen */}
      <SimulatedFullscreenExit isVisible={isSimulatedFullScreen} />

      <main className={`${isFullScreen ? "" : "pt-16"}`}>
        {children}
      </main>
    </div>
  );
}
