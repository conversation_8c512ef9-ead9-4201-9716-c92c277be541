import { MainLayout } from "@/components/layout/main-layout";
import { SEOContent } from "@/components/seo/seo-content";
import { StructuredData } from "@/components/seo/structured-data";
import { ClockClient } from "./clock-client";
import { FullscreenDebug } from "@/components/debug/fullscreen-debug";

export default function HomePage() {
  return (
    <MainLayout>
      <StructuredData type="homepage" />

      {/* Full-height clock section */}
      <section className="relative" style={{ height: 'calc(100vh - 4rem)' }}>
        <ClockClient />
      </section>

      {/* SEO content section - below the fold */}
      <section className="relative z-9" data-seo-content>
        <SEOContent />
      </section>

      {/* Debug component for testing fullscreen on mobile */}
      <FullscreenDebug />
    </MainLayout>
  );
}
