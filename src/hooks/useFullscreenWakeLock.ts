import { useState, useEffect } from 'react';
import { useWakeLock } from './useWakeLock';

export function useFullscreenWakeLock() {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const { requestWakeLock, releaseWakeLock, isSupported, isActive } = useWakeLock();

  // Monitor fullscreen state and manage wake lock
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullScreenNow = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
      );
      setIsFullScreen(isFullScreenNow);

      // Manage wake lock based on fullscreen state
      if (isFullScreenNow) {
        requestWakeLock();
      } else {
        releaseWakeLock();
      }

      // Hide/show SEO content based on fullscreen state
      const seoSection = document.querySelector('[data-seo-content]');
      if (seoSection) {
        if (isFullScreenNow) {
          (seoSection as HTMLElement).style.display = 'none';
        } else {
          (seoSection as HTMLElement).style.display = 'block';
        }
      }
    };

    // Add event listeners for different browsers
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("mozfullscreenchange", handleFullscreenChange);
    document.addEventListener("MSFullscreenChange", handleFullscreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener("webkitfullscreenchange", handleFullscreenChange);
      document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
      document.removeEventListener("MSFullscreenChange", handleFullscreenChange);
    };
  }, [requestWakeLock, releaseWakeLock]);

  // Handle page visibility changes to re-request wake lock if needed
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isFullScreen && !isActive) {
        requestWakeLock();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isFullScreen, isActive, requestWakeLock]);

  return {
    isFullScreen,
    isWakeLockSupported: isSupported,
    isWakeLockActive: isActive,
  };
} 